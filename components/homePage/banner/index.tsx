"use client"

import React, { useEffect, useState } from "react"
import { Icon } from "@/components/common/Icon"
import { useLanguageStore } from "@/stores/languageStore"
import { useSystemSetting } from "@/stores"


export default function Banner() {
  const [data, setData] = useState<any>(null)
  const { isRTL } = useLanguageStore()
  const headline = useSystemSetting("HERO_BANNER_HEADLINE")
  const description = useSystemSetting("HERO_BANNER_DESCRIPTION")
  const ctaText = useSystemSetting("HERO_BANNER_CTA_TEXT")
  const ctaUrl = useSystemSetting("HERO_BANNER_CTA_URL")
  const videoUrl = useSystemSetting("HERO_BANNER_VIDEO_URL")

  useEffect(() => {
    setData({
      headline,
      description,
      ctaText,
      ctaUrl,
      videoUrl
    })
  }, [headline, description, ctaText, ctaUrl, videoUrl])
  return (
    <div className="relative w-full h-screen overflow-hidden">
      <video
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        poster={"/images/jewelry-banner-poster.jpg"}
      >
        {/* Always render <source> with fallback empty string to avoid hydration mismatch */}
        <source src={"/videos/jewelry-banner.mp4"} type="video/mp4" />
      </video>

      {/* Dark overlay for text readability */}
      <div className="absolute inset-0 bg-black/28" />

      {/* Content Container */}
      <div className="relative z-10 flex items-end justify-center h-full  lg:px-8">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-8 items-center min-h-[17.5vh]">
            <>
              <h1 className="justify-start text-primary-50 text-5xl font-normal leading-[56px]">
                {data?.headline}
              </h1>
              <div>
                <p className="text-lg sm:text-xl text-primary-50 leading-relaxed mb-8">
                  {data?.description}
                </p>
                <a href={data?.ctaUrl} className="flex items-center gap-1">
                  <p className="text-lg sm:text-xl text-primary-300">
                    {data?.ctaText}
                  </p>
                  <Icon
                    name={isRTL ? "arrow-left" : "arrow-right"}
                    size={20}
                    className="ml-2 text-primary-300"
                  />
                </a>
              </div>
            </>
            {/* )} */}
          </div>
        </div>
      </div>
    </div>
  )
}
