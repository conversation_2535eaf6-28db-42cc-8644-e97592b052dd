"use client"

import { useTranslation } from "react-i18next"
import { useLanguageStore } from "@/stores/languageStore"
import { Icon } from "@/components/common/Icon"
import { useSystemSettingsStore } from "@/stores"
import { cn } from "@/lib/utils"

interface PromoBannerProps {
  speed?: "slow" | "normal" | "fast" | "very-fast"
}

export function PromoBanner({ speed = "normal" }: PromoBannerProps) {
  const { t } = useTranslation()
  const { isRTL } = useLanguageStore()
  const { getSettingByKey } = useSystemSettingsStore()
  const listText = getSettingByKey("PROMOTION_BANNER_LIST_TEXT")?.value || []

  if (!listText) return null

  return (
    <section className="relative w-full overflow-hidden bg-primary-400 dark:bg-primary-100 text-custom-borderLight  dark:border dark:border-primary-100 dark:text-secondary-500 ">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_20%_50%,rgba(255,255,255,0.3),transparent_50%)]" />
        <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_80%_50%,rgba(255,255,255,0.2),transparent_50%)]" />
      </div>

      {/* Decorative Elements */}

      {/* Marquee Container */}
      <div
        className={`marquee-container relative py-4 h-16 flex items-center marquee-${speed}`}
      >
        <div className="flex items-center whitespace-nowrap">
          {/* Multiple copies of the text for seamless loop */}
          <div
            className={cn(
              `inline-flex items-center`,
              isRTL ? "marquee-rtl" : "marquee"
            )}
          >
            {/* Repeat the content multiple times for seamless infinite loop */}
            {(listText || [])?.map((item: any, index: any) => (
              <div key={index} className="flex items-center">
                <span className="text-custom-borderLight dark:text-secondary-500 text-lg md:text-xl font-medium px-6">
                  {item}
                </span>

                {/* Diamond Icon */}
                <div className="px-4">
                  <Icon name="vector" size={8} className="" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-black/10 to-transparent"></div>
    </section>
  )
}
