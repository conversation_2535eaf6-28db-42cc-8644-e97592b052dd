"use client"

import { Head<PERSON> } from "./Header"
import { Footer } from "./Footer"

interface MainLayoutProps {
  children: React.ReactNode
  isBlogDetailsPage?: boolean
  isLiveMarketInsights?: boolean
}

export function MainLayout({
  children,
  isBlogDetailsPage = false,
  isLiveMarketInsights = false
}: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-white-50 dark:bg-dark-secondary-600 transition-colors duration-300">
      <Header isBlogDetailsPage={isBlogDetailsPage}
      isLiveMarketInsights={isLiveMarketInsights} />

      {/* Main content area */}
      <main className="flex-1  min-h-[80vh]">{children}</main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
