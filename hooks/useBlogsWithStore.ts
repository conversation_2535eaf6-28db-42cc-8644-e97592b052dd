'use client'

import { useEffect } from 'react'
import { useBlogs, useBlogBySlug, useBlogByPopular } from '@/hooks/api'
import { useBlogStoreRQ } from '@/stores/blogStoreRQ'
import { BlogsParams } from '@/types/api'

export const useBlogsWithStore = (params: BlogsParams & { enabled?: boolean } = {}) => {
  const { setBlogs, setPagination } = useBlogStoreRQ()

  const { enabled, ...blogParams } = params
  const query = useBlogs(blogParams, {
    enabled: enabled !== false && typeof window !== 'undefined'
  })

  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setBlogs(query.data.items || [])
      setPagination({
        current_page: query.data.current_page,
        last_page: query.data.last_page,
        per_page: query.data.per_page,
        total: query.data.total,
      })
    }
  }, [query.data, setBlogs, setPagination])

  return {
    ...query,
    // Expose store methods for backward compatibility
    getBlogBySlug: useBlogStoreRQ(state => state.getBlogBySlug),
    getFeaturedBlogs: useBlogStoreRQ(state => state.getFeaturedBlogs),
    getBlogsByCategory: useBlogStoreRQ(state => state.getBlogsByCategory),
  }
}

export const useBlogBySlugWithStore = (slug: string) => {
  const { setCurrentBlog } = useBlogStoreRQ()

  const query = useBlogBySlug(slug)

  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setCurrentBlog(query.data)
    }
  }, [query.data, setCurrentBlog])

  return {
    ...query,
    // Expose store methods for backward compatibility
    clearCurrentBlog: useBlogStoreRQ(state => state.clearCurrentBlog),
  }
}


export const useBlogPopularWithStore = (limit: number) => {
  const { setCurrentBlog } = useBlogStoreRQ()

  const query = useBlogByPopular(limit)

  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setCurrentBlog(query.data)
    }
  }, [query.data, setCurrentBlog])

  return {
    ...query,
    // Expose store methods for backward compatibility
    clearCurrentBlog: useBlogStoreRQ(state => state.clearCurrentBlog),
  }
}

